import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { OnboardingStepId } from '../types'
import { ONBOARDING_STEPS } from '../constants'

export class OnboardingNavigationService {
  private router: AppRouterInstance

  constructor(router: AppRouterInstance) {
    this.router = router
  }

  /**
   * Derives the current step from the URL pathname
   */
  getCurrentStep(pathname: string): OnboardingStepId {
    const currentStep = ONBOARDING_STEPS.find(s => s.path === pathname)
    return currentStep?.id || 'personal-info'
  }

  /**
   * Checks if navigation is allowed from the current step in the specified direction
   */
  canNavigate(from: OnboardingStepId, direction: 'next' | 'prev'): boolean {
    const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === from)
    
    if (currentIndex === -1) return false
    
    if (direction === 'next') {
      return currentIndex < ONBOARDING_STEPS.length - 1
    } else {
      return currentIndex > 0
    }
  }

  /**
   * Navigates from the current step in the specified direction
   * Returns true if navigation was successful, false otherwise
   */
  navigate(from: OnboardingStepId, direction: 'next' | 'prev'): boolean {
    if (!this.canNavigate(from, direction)) {
      return false
    }

    const currentIndex = ONBOARDING_STEPS.findIndex(s => s.id === from)
    const targetIndex = direction === 'next' ? currentIndex + 1 : currentIndex - 1
    const targetStep = ONBOARDING_STEPS[targetIndex]

    if (!targetStep) {
      return false
    }

    this.router.push(targetStep.path)
    return true
  }

  /**
   * Navigates directly to a specific step
   * Returns true if navigation was successful, false if step doesn't exist
   */
  goToStep(stepId: OnboardingStepId): boolean {
    const step = ONBOARDING_STEPS.find(s => s.id === stepId)
    
    if (!step) {
      return false
    }

    this.router.push(step.path)
    return true
  }

  /**
   * Gets the path for a specific step
   * Returns the path string or null if step doesn't exist
   */
  getStepPath(stepId: OnboardingStepId): string | null {
    const step = ONBOARDING_STEPS.find(s => s.id === stepId)
    return step?.path || null
  }

}