'use client'

import { OnboardingStepHeader, OnboardingSection } from './'
import { useOnboardingStore, useOnboardingNavigation } from '../hooks'
import { OnboardingStepId } from '../types'

interface PlaceholderPageProps {
  stepId: OnboardingStepId
  title: string
  description: string
  sectionTitle: string
  sectionDescription: string
}

export function PlaceholderPage({
  stepId,
  title,
  description,
  sectionTitle,
  sectionDescription
}: PlaceholderPageProps) {
  const { goNext, goBack, canGoBack } = useOnboardingNavigation()
  const { markStepComplete } = useOnboardingStore()

  const handleSave = () => markStepComplete(stepId)
  const handleContinue = () => goNext()
  const handleBack = () => goBack()

  return (
    <div>
      <OnboardingStepHeader
        title={title}
        description={description}
        onBack={canGoBack ? handleBack : undefined}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <div className="space-y-8">
        <OnboardingSection
          title={sectionTitle}
          description={sectionDescription}
        />
      </div>
    </div>
  )
}