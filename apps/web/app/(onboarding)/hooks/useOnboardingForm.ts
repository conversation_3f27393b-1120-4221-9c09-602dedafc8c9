'use client'

import { useCallback, useMemo } from 'react'
import { useOnboardingStore } from '../../../stores/onboarding'
import { useOnboardingNavigation } from './useOnboardingNavigation'
import { OnboardingStepId, OnboardingFormData } from '../types'

function debounce(
  func: (stepId: OnboardingStepId, formData: Record<string, unknown>) => void,
  delay: number
): (stepId: OnboardingStepId, formData: Record<string, unknown>) => void {
  let timeoutId: NodeJS.Timeout
  return (stepId: OnboardingStepId, formData: Record<string, unknown>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(stepId, formData), delay)
  }
}

type StepDataMap = {
  'personal-info': OnboardingFormData['personalInfo']
  'situation': OnboardingFormData['situation']
  'home': OnboardingFormData['homeInfo']
  'assets': OnboardingFormData['assets']
  'liabilities': OnboardingFormData['liabilities']
  'insurance': OnboardingFormData['insurance']
  'income': OnboardingFormData['income']
  'expenses': OnboardingFormData['expenses']
  'review': undefined
}

export interface UseOnboardingFormReturn<T extends OnboardingStepId> {
  // Data and state
  formData: StepDataMap[T]
  errors: Record<string, string>

  // Form actions
  updateField: (field: string, value: string | number | boolean | string[]) => void

  // Validation
  validate: () => boolean
  isValid: boolean

  // Navigation actions  
  handleSave: () => void
  handleContinue: () => void
  handleBack: () => void

  // Navigation state
  canGoBack: boolean
  canGoNext: boolean
}

export function useOnboardingForm<T extends OnboardingStepId>(
  stepId: T
): UseOnboardingFormReturn<T> {
  const {
    data,
    errors,
    updateField: storeUpdateField,
    validateStep,
    markStepComplete,
    updateData
  } = useOnboardingStore()

  const { goNext, goBack, canGoNext, canGoBack } = useOnboardingNavigation()

  // Get step-specific data
  const sectionKey = stepId === 'personal-info' ? 'personalInfo' :
    stepId === 'home' ? 'homeInfo' :
      stepId as keyof OnboardingFormData

  const formData = data[sectionKey] as StepDataMap[T]

  // Debounced auto-save function
  const debouncedSave = useMemo(
    () => debounce((stepId: OnboardingStepId, formData: Record<string, unknown>) => {
      updateData(sectionKey, formData)
    }, 1000),
    [sectionKey, updateData]
  )

  // Update field with optimistic updates
  const updateField = useCallback((field: string, value: string | number | boolean | string[]) => {
    storeUpdateField(stepId, field, value)

    // Auto-save after debounce delay
    const currentData = { ...formData, [field]: value }
    debouncedSave(stepId, currentData)
  }, [stepId, storeUpdateField, formData, debouncedSave])

  // Validate current step
  const validate = useCallback(() => {
    return validateStep(stepId)
  }, [stepId, validateStep])

  // Check if form is currently valid
  const isValid = useMemo(() => {
    const currentStepErrors = errors[stepId] || {}
    return Object.keys(currentStepErrors).length === 0 ||
      Object.values(currentStepErrors).every(error => !error)
  }, [errors, stepId])

  // Save current form data (immediate save)
  const handleSave = useCallback(() => {
    updateData(sectionKey, formData)
    markStepComplete(stepId)
  }, [sectionKey, formData, updateData, markStepComplete, stepId])

  // Continue to next step (validate first)
  const handleContinue = useCallback(() => {
    if (validate()) {
      updateData(sectionKey, formData)
      markStepComplete(stepId)
      goNext()
    }
  }, [validate, sectionKey, formData, updateData, markStepComplete, stepId, goNext])

  // Go back to previous step
  const handleBack = useCallback(() => {
    // Auto-save current progress before going back
    updateData(sectionKey, formData)
    goBack()
  }, [sectionKey, formData, updateData, goBack])

  return {
    // Data and state
    formData: formData || ({} as StepDataMap[T]),
    errors: errors[stepId] || {},

    // Form actions
    updateField,

    // Validation
    validate,
    isValid,

    // Navigation actions
    handleSave,
    handleContinue,
    handleBack,

    // Navigation state
    canGoBack,
    canGoNext
  }
}