import { z } from 'zod'

export const personalInfoSchema = z.object({
  firstName: z.string().min(2, 'Must be at least 2 characters').max(50, 'Too long'),
  middleName: z.string().max(50, 'Too long').optional(),
  lastName: z.string().min(2, 'Must be at least 2 characters').max(50, 'Too long'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  maritalStatus: z.enum(['single', 'married', 'divorced', 'widowed', 'separated'])
})

export const situationSchema = z.object({
  selectedCharacteristics: z.array(z.string()).optional(),
  retirementAge: z.number().min(50, 'Must be at least 50').max(100, 'Must be reasonable').optional(),
  targetSavings: z.number().min(0, 'Must be positive').optional(),
  riskTolerance: z.enum(['conservative', 'moderate', 'aggressive']).optional(),
  investmentTimeline: z.number().min(1, 'Must be at least 1 year').optional(),
  primaryGoals: z.array(z.string()).optional(),
  currentNetWorth: z.number().optional()
})

export const homeInfoSchema = z.object({
  housingStatus: z.enum(['own', 'rent']).nullable(),
  // Owner fields
  address: z.string().optional(),
  estimatedValue: z.number().min(0, 'Must be positive').optional(),
  dateAcquired: z.string().optional(),
  // Renter fields  
  rentPayment: z.number().min(0, 'Must be positive').optional(),
  rentInterval: z.enum(['weekly', 'monthly', 'yearly']).optional()
}).superRefine((data, ctx) => {
  // Conditional validation based on housing status
  if (data.housingStatus === 'own') {
    if (!data.address?.trim()) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['address'],
        message: 'Property address is required'
      })
    }
    if (!data.estimatedValue || data.estimatedValue <= 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['estimatedValue'], 
        message: 'Estimated value is required'
      })
    }
    if (!data.dateAcquired?.trim()) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['dateAcquired'],
        message: 'Date acquired is required'
      })
    }
  }
  
  if (data.housingStatus === 'rent') {
    if (!data.rentPayment || data.rentPayment <= 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['rentPayment'],
        message: 'Rent payment is required'
      })
    }
    if (!data.rentInterval) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['rentInterval'],
        message: 'Payment interval is required'
      })
    }
  }
})

export const assetsSchema = z.object({
  checkingAccounts: z.number().min(0, 'Must be positive').optional(),
  savingsAccounts: z.number().min(0, 'Must be positive').optional(),
  investmentAccounts: z.number().min(0, 'Must be positive').optional(),
  retirementAccounts: z.number().min(0, 'Must be positive').optional(),
  otherAssets: z.number().min(0, 'Must be positive').optional(),
  otherAssetsDescription: z.string().optional()
})

export const liabilitiesSchema = z.object({
  creditCards: z.number().min(0, 'Must be positive').optional(),
  studentLoans: z.number().min(0, 'Must be positive').optional(),
  autoLoans: z.number().min(0, 'Must be positive').optional(),
  personalLoans: z.number().min(0, 'Must be positive').optional(),
  otherDebt: z.number().min(0, 'Must be positive').optional(),
  otherDebtDescription: z.string().optional()
})

export const insuranceSchema = z.object({
  healthInsurance: z.boolean().optional(),
  lifeInsurance: z.number().min(0, 'Must be positive').optional(),
  disabilityInsurance: z.boolean().optional(),
  autoInsurance: z.number().min(0, 'Must be positive').optional(),
  umbrellaPolicy: z.boolean().optional(),
  otherInsurance: z.string().optional()
})

export const incomeSchema = z.object({
  salary: z.number().min(0, 'Must be positive'),
  bonuses: z.number().min(0, 'Must be positive'),
  businessIncome: z.number().min(0, 'Must be positive').optional(),
  investmentIncome: z.number().min(0, 'Must be positive').optional(),
  rentalIncome: z.number().min(0, 'Must be positive').optional(),
  otherIncome: z.number().min(0, 'Must be positive'),
  otherIncomeDescription: z.string().optional()
})

export const expensesSchema = z.object({
  housing: z.number().min(0, 'Must be positive'),
  transportation: z.number().min(0, 'Must be positive'),
  food: z.number().min(0, 'Must be positive'),
  utilities: z.number().min(0, 'Must be positive'),
  healthcare: z.number().min(0, 'Must be positive').optional(),
  insurance: z.number().min(0, 'Must be positive').optional(),
  entertainment: z.number().min(0, 'Must be positive'),
  shopping: z.number().min(0, 'Must be positive').optional(),
  childcare: z.number().min(0, 'Must be positive').optional(),
  other: z.number().min(0, 'Must be positive'),
  otherDescription: z.string().optional()
})

// Schema mapping by step ID
export const validationSchemas = {
  'personal-info': personalInfoSchema,
  'situation': situationSchema,
  'home': homeInfoSchema,
  'assets': assetsSchema,
  'liabilities': liabilitiesSchema,
  'insurance': insuranceSchema,
  'income': incomeSchema,
  'expenses': expensesSchema
} as const

export type ValidationSchemas = typeof validationSchemas
export type StepValidationSchema<T extends keyof ValidationSchemas> = ValidationSchemas[T]