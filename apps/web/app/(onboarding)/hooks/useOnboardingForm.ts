'use client'

import { useCallback, useMemo, useEffect } from 'react'
import { useOnboardingStore } from '../../../stores/onboarding'
import { useOnboardingNavigation } from './useOnboardingNavigation'
import { OnboardingStepId, OnboardingFormData } from '../types'

/**
 * Maps step IDs to their corresponding data section keys
 * This centralizes the mapping logic and ensures consistency
 * with the store implementation.
 */
function getSectionKey(stepId: OnboardingStepId): keyof OnboardingFormData {
  switch (stepId) {
    case 'personal-info':
      return 'personalInfo'
    case 'home':
      return 'homeInfo'
    default:
      return stepId as keyof OnboardingFormData
  }
}

type StepDataMap = {
  'personal-info': OnboardingFormData['personalInfo']
  'situation': OnboardingFormData['situation']
  'home': OnboardingFormData['homeInfo']
  'assets': OnboardingFormData['assets']
  'liabilities': OnboardingFormData['liabilities']
  'insurance': OnboardingFormData['insurance']
  'income': OnboardingFormData['income']
  'expenses': OnboardingFormData['expenses']
  'review': undefined
}

export interface UseOnboardingFormReturn<T extends OnboardingStepId> {
  // Data and state
  formData: StepDataMap[T]
  errors: Record<string, string>

  // Form actions
  updateField: (field: string, value: string | number | boolean | string[]) => void

  // Validation
  validate: () => boolean
  isValid: boolean

  // Navigation actions
  handleSave: () => void
  handleContinue: () => void
  handleBack: () => void

  // Navigation state
  canGoBack: boolean
  canGoNext: boolean
}

/**
 * Enhanced useOnboardingForm hook with store-level debouncing
 *
 * This hook provides a clean interface for onboarding form management while
 * eliminating the React closure and debouncing issues that existed in the previous
 * implementation.
 *
 * Key improvements:
 * 1. **Store-Level Debouncing**: Auto-save debouncing is now handled at the store level,
 *    eliminating React closure issues and ensuring consistent behavior across components.
 *
 * 2. **Simplified Component Logic**: Components no longer need to manage debouncing,
 *    dependency arrays, or stale closure issues. Just call updateField and the store
 *    handles the rest.
 *
 * 3. **Automatic Cleanup**: The hook automatically cancels pending auto-saves when
 *    the component unmounts, preventing memory leaks and unnecessary API calls.
 *
 * 4. **Centralized Mapping**: Step ID to section key mapping is centralized and
 *    consistent between the hook and store.
 *
 * 5. **Current Data Access**: All methods that need form data get it from the current
 *    store state rather than captured closures, eliminating stale data issues.
 *
 * Architecture:
 * - updateField() -> store.updateField() -> immediate store update + debounced auto-save
 * - handleSave/Continue/Back() -> get current data from store -> explicit save
 * - Auto-save is debounced at 1 second intervals per section
 * - All pending auto-saves are cancelled on component unmount
 *
 * @param stepId - The current onboarding step identifier
 * @returns Hook interface with form data, actions, and navigation methods
 */
export function useOnboardingForm<T extends OnboardingStepId>(
  stepId: T
): UseOnboardingFormReturn<T> {
  const {
    data,
    errors,
    updateField: storeUpdateField,
    validateStep,
    markStepComplete,
    updateData,
    _cancelAutoSave
  } = useOnboardingStore()

  const { goNext, goBack, canGoNext, canGoBack } = useOnboardingNavigation()

  // Get step-specific data using centralized mapping
  const sectionKey = getSectionKey(stepId)
  const formData = data[sectionKey] as StepDataMap[T]

  /**
   * Cleanup: Cancel any pending auto-saves when component unmounts
   * This prevents memory leaks and unnecessary API calls after unmount
   */
  useEffect(() => {
    return () => {
      _cancelAutoSave()
    }
  }, [_cancelAutoSave])

  /**
   * Update field with immediate store update and automatic debounced persistence
   *
   * This simplified implementation:
   * 1. Calls the store's updateField method which handles both immediate update and debounced save
   * 2. Eliminates React closure issues by moving debouncing to the store level
   * 3. Ensures consistent behavior across all form components
   * 4. No longer needs formData in dependencies, preventing unnecessary re-renders
   */
  const updateField = useCallback((field: string, value: string | number | boolean | string[]) => {
    // The store handles both immediate update and debounced auto-save
    storeUpdateField(stepId, field, value)
  }, [stepId, storeUpdateField])

  // Validate current step
  const validate = useCallback(() => {
    return validateStep(stepId)
  }, [stepId, validateStep])

  // Check if form is currently valid
  const isValid = useMemo(() => {
    const currentStepErrors = errors[stepId] || {}
    return Object.keys(currentStepErrors).length === 0 ||
      Object.values(currentStepErrors).every(error => !error)
  }, [errors, stepId])

  /**
   * Save current form data (immediate save)
   * Gets current data from store to avoid stale closure issues
   */
  const handleSave = useCallback(() => {
    const currentData = data[sectionKey]
    if (currentData) {
      updateData(sectionKey, currentData)
    }
    markStepComplete(stepId)
  }, [sectionKey, data, updateData, markStepComplete, stepId])

  /**
   * Continue to next step (validate first)
   * Uses current store data to avoid stale closure issues
   */
  const handleContinue = useCallback(() => {
    if (validate()) {
      const currentData = data[sectionKey]
      if (currentData) {
        updateData(sectionKey, currentData)
      }
      markStepComplete(stepId)
      goNext()
    }
  }, [validate, sectionKey, data, updateData, markStepComplete, stepId, goNext])

  /**
   * Go back to previous step
   * Auto-saves current progress using current store data
   */
  const handleBack = useCallback(() => {
    const currentData = data[sectionKey]
    if (currentData) {
      updateData(sectionKey, currentData)
    }
    goBack()
  }, [sectionKey, data, updateData, goBack])

  return {
    // Data and state
    formData: formData || ({} as StepDataMap[T]),
    errors: errors[stepId] || {},

    // Form actions
    updateField,

    // Validation
    validate,
    isValid,

    // Navigation actions
    handleSave,
    handleContinue,
    handleBack,

    // Navigation state
    canGoBack,
    canGoNext
  }
}