import { OnboardingStep } from './types'

export const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'personal-info',
    title: 'Personal Info',
    description: 'Tell us about yourself',
    path: '/personal-info'
  },
  {
    id: 'situation',
    title: 'Situation',
    description: 'Your current financial situation and goals',
    path: '/situation'
  },
  {
    id: 'home',
    title: 'Home',
    description: 'Property and real estate information',
    path: '/home'
  },
  {
    id: 'assets',
    title: 'Assets',
    description: 'Your investments and savings',
    path: '/assets'
  },
  {
    id: 'liabilities',
    title: 'Liabilities',
    description: 'Debts and financial obligations',
    path: '/liabilities'
  },
  {
    id: 'insurance',
    title: 'Insurance',
    description: 'Insurance policies and coverage',
    path: '/insurance'
  },
  {
    id: 'income',
    title: 'Income',
    description: 'Your sources of income',
    path: '/income'
  },
  {
    id: 'expenses',
    title: 'Expenses',
    description: 'Monthly spending and costs',
    path: '/expenses'
  },
  {
    id: 'review',
    title: 'Review',
    description: 'Review and complete your profile',
    path: '/review'
  }
] as const