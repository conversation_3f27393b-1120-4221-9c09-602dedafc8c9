import { Button } from '@finpro/ui'

interface OnboardingStepHeaderProps {
  title: string
  description?: string
  onBack?: () => void
  onSave?: () => void
  onContinue?: () => void
  continueText?: string
}

export function OnboardingStepHeader({
  title,
  description,
  onBack,
  onSave,
  onContinue,
  continueText = "Next →"
}: OnboardingStepHeaderProps) {
  return (
    <div className="flex items-start justify-between mb-8">
      <div>
        <h1 className="text-2xl font-bold text-foreground mb-2">{title}</h1>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      
      <div className="flex items-center gap-3">
        {onBack && (
          <Button variant="outlined" onClick={onBack}>
            ← Previous
          </Button>
        )}
        
        {onSave && (
          <Button 
            variant="ghost" 
            onClick={onSave}
          >
            Mark Complete
          </Button>
        )}
        
        {onContinue && (
          <Button 
            onClick={onContinue}
          >
            {continueText}
          </Button>
        )}
      </div>
    </div>
  )
}