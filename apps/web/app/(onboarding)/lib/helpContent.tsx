import { OnboardingStepId } from '../types'

export interface HelpSection {
  title: string
  content: string
  tips?: string[]
  icon?: string
}

export interface StepHelpContent {
  title: string
  description: string
  sections: HelpSection[]
}

export const STEP_HELP_CONTENT: Record<OnboardingStepId, StepHelpContent> = {
  'personal-info': {
    title: 'Personal Information',
    description: 'We need this info to verify your identity and personalize your experience.',
    sections: [
      {
        title: '🆔 Legal Name',
        content: 'Use your exact legal name as it appears on your ID or tax documents.',
        tips: [
          'Must match bank accounts for linking',
          'Middle name is optional'
        ]
      },
      {
        title: '📅 Date of Birth',
        content: 'Used for identity verification and age-appropriate recommendations.',
        tips: [
          'Must be 18+ to create an account',
          'Affects retirement planning'
        ]
      },
      {
        title: '💑 Marital Status',
        content: 'Affects tax strategies and joint financial planning options.',
        tips: [
          'Can be updated if status changes'
        ]
      }
    ]
  },

  'situation': {
    title: 'Your Situation', 
    description: 'Select characteristics that apply to customize your recommendations.',
    sections: [
      {
        title: '🏷️ Choose What Applies',
        content: 'Select all that describe your current situation.',
        tips: [
          'Multiple selections are fine',
          'Helps personalize your advice'
        ]
      },
      {
        title: '👨‍👩‍👧‍👦 Life Stage Impact',
        content: 'Family and career status influence your financial strategy.',
        tips: [
          'Married/Kids: Joint planning & education savings',
          'Military/Gov: Special benefits available',
          'Business Owner: Variable income strategies'
        ]
      }
    ]
  },

  'home': {
    title: 'Housing Information',
    description: 'Housing is typically your largest asset or expense.',
    sections: [
      {
        title: '🏠 Own vs Rent',
        content: 'Each has different financial implications for your overall strategy.',
        tips: [
          'Owners: Build equity, tax benefits',
          'Renters: Flexibility, lower upfront costs'
        ]
      },
      {
        title: '💡 Planning Impact',
        content: 'Housing costs should be 25-30% of your gross income.',
        tips: [
          'Home equity counts toward net worth',
          'Consider maintenance and tax costs'
        ]
      }
    ]
  },

  // Simplified content for other steps
  'assets': {
    title: 'Assets & Investments',
    description: 'Tell us about your current investments and savings.',
    sections: [
      {
        title: '📈 What to Include',
        content: 'List all investment and savings accounts.',
        tips: ['Include retirement accounts', 'Don\'t forget cash savings']
      }
    ]
  },

  'liabilities': {
    title: 'Debts & Liabilities',
    description: 'Help us understand your current debts.',
    sections: [
      {
        title: '💳 All Debts',
        content: 'Include all loans, credit cards, and other debts.',
        tips: ['Note interest rates if known', 'Include minimum payments']
      }
    ]
  },

  'insurance': {
    title: 'Insurance Coverage',
    description: 'Insurance protects your financial plan.',
    sections: [
      {
        title: '🛡️ Current Coverage',
        content: 'Tell us about your existing insurance policies.',
        tips: ['Include employer benefits', 'Note coverage amounts']
      }
    ]
  },

  'income': {
    title: 'Income Sources',
    description: 'Document all your sources of income.',
    sections: [
      {
        title: '💰 All Income',
        content: 'Include salary, bonuses, and other regular income.',
        tips: ['Use pre-tax amounts', 'Include irregular income']
      }
    ]
  },

  'expenses': {
    title: 'Monthly Expenses',
    description: 'Track your regular monthly spending.',
    sections: [
      {
        title: '📊 Monthly Costs',
        content: 'Estimate your average monthly expenses.',
        tips: ['Include all regular bills', 'Estimate variable costs']
      }
    ]
  },

  'review': {
    title: 'Review & Submit',
    description: 'Review everything before completing your profile.',
    sections: [
      {
        title: '✅ Final Check',
        content: 'Make sure all information is accurate.',
        tips: ['Double-check numbers', 'You can update later']
      }
    ]
  }
}