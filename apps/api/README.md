# FinPro API

FastAPI backend application for the FinPro financial management platform.

## Overview

This is a RESTful API built with FastAPI that provides backend services for financial data management, user authentication, and business logic processing.

## Technology Stack

- **Framework**: FastAPI
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Migration**: Alembic
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Pydantic
- **ASGI Server**: Uvicorn

## Project Structure

```
api/
├── app/
│   ├── api/              # API endpoints and routing
│   │   └── v1/           # API version 1
│   │       ├── endpoints/    # Individual endpoint modules
│   │       └── router.py     # Main API router
│   ├── core/             # Core application components
│   │   ├── config.py         # Application configuration
│   │   ├── database.py       # Database connection setup
│   │   └── security.py       # Security utilities
│   ├── models/           # Data models
│   │   ├── domain/           # Domain models
│   │   ├── schemas/          # Pydantic schemas
│   │   └── database/         # SQLAlchemy models
│   ├── services/         # Business logic layer
│   ├── middleware/       # Custom middleware
│   ├── utils/           # Utility functions
│   └── main.py          # Application entry point
├── alembic/             # Database migrations
├── tests/               # Test suite
├── requirements.txt     # Python dependencies
├── pyproject.toml      # Project configuration
├── alembic.ini         # Alembic configuration
├── Dockerfile          # Container configuration
└── .env.example        # Environment variables template
```

## Setup Instructions

### Prerequisites

- Python 3.11+
- PostgreSQL 14+
- Poetry

### Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd finpro/apps/api
   ```

2. **Create a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   poetry install
   ```

4. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your configuration values.

5. **Set up the database**:
   ```bash
   # Create database (if not exists)
   createdb finpro_db
   
   # Run migrations
   alembic upgrade head
   ```

## Running the Application

### Using Docker

```bash
# Build the image
docker build -t finpro-api .

# Run the container
docker run -p 8000:8000 --env-file .env finpro-api
```

## API Documentation

Once the application is running, you can access:

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI Schema**: `http://localhost:8000/api/v1/openapi.json`

## Database Migrations

### Create a new migration

```bash
alembic revision --autogenerate -m "Description of changes"
```

### Apply migrations

```bash
alembic upgrade head
```

### Rollback migrations

```bash
alembic downgrade -1  # Rollback one migration
alembic downgrade base  # Rollback all migrations
```

## Testing

### Run all tests

```bash
pytest
```

### Run with coverage

```bash
pytest --cov=app --cov-report=html
```

### Run specific test file

```bash
pytest tests/test_users.py
```

## Development Guidelines

### Code Style

- Follow PEP 8 guidelines
- Use type hints for all functions
- Maximum line length: 88 characters (Black formatter)

### Format code

```bash
black app/
```

### Lint code

```bash
flake8 app/
```

### Type checking

```bash
mypy app/
```

## Environment Variables

Key environment variables (see `.env.example` for full list):

| Variable | Description | Default |
|----------|-------------|---------|
| `PROJECT_NAME` | Application name | FinPro API |
| `API_V1_STR` | API version prefix | /api/v1 |
| `SECRET_KEY` | JWT secret key | (required) |
| `POSTGRES_SERVER` | Database host | localhost |
| `POSTGRES_USER` | Database user | finpro |
| `POSTGRES_PASSWORD` | Database password | (required) |
| `POSTGRES_DB` | Database name | finpro_db |
| `BACKEND_CORS_ORIGINS` | Allowed CORS origins | ["http://localhost:3000"] |

## API Endpoints

### Health Check
- `GET /` - API status
- `GET /health` - Health check endpoint

## Security

- JWT-based authentication
- Password hashing with bcrypt
- CORS configuration for frontend integration
- Environment-based configuration
- SQL injection prevention via SQLAlchemy ORM

## Troubleshooting

### Database Connection Issues
- Verify PostgreSQL is running: `pg_isready`
- Check database credentials in `.env`
- Ensure database exists: `psql -U postgres -l`

### Migration Issues
- Check alembic.ini configuration
- Verify database connection string
- Review migration files in `alembic/versions/`

### Import Errors
- Ensure virtual environment is activated
- Verify all dependencies are installed
- Check Python version compatibility (3.11+)

## Contributing

1. Create a feature branch
2. Make your changes
3. Add/update tests
4. Run linting and tests
5. Submit a pull request

## License

[License information here]

## Support

For issues and questions, please contact the development team or create an issue in the repository.