# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Start development server with Turbopack
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run ESLint
npm run lint
```

## Application Architecture

### Next.js 15 App Router Structure
- Uses App Router with route groups for organization
- `(auth)/` - Authentication routes (login, register, verify-email)
- `(onboarding)/` - Multi-step onboarding flow
- `client/dashboard/` - Main dashboard application

### Onboarding Flow Architecture
The onboarding system is a comprehensive multi-step wizard with:

**Route Groups**: `/apps/web/app/(onboarding)/`
- `personal-info/` - Personal information collection
- `situation/` - Financial situation assessment  
- `home/` - Home ownership details
- `assets/` - Asset management
- `income/` - Income tracking
- `expenses/` - Expense management
- `liabilities/` - Debt and liability tracking
- `insurance/` - Insurance coverage
- `review/` - Final review step

**State Management**: `hooks/useOnboardingFlow.ts`
- Zustand-based state management for onboarding progress
- Step navigation, completion tracking, and data persistence
- Progress calculation and validation

**Shared Layout**: `(onboarding)/layout.tsx`
- Three-column layout with navigation sidebar, main content, and help sidebar
- Progress tracking header with KPI summary
- Step navigation breadcrumbs
- Responsive design with consistent styling

### Key Patterns

**Component Organization**:
- `components/` - Reusable components (forms, UI elements)
- Domain-specific components in respective route folders
- Index files for clean imports

**Form Architecture**:
- `forms/` directory with individual form components
- `validation.ts` - Centralized form validation schemas
- Form state management integrated with global onboarding state

**Help System**:
- `HelpSidebar` component provides contextual help
- `lib/helpContent.tsx` - Step-specific help content
- Dynamic help content based on current onboarding step

## Available UI Components

Import components from `@finpro/ui`:

```tsx
import { Button, Input, Dialog } from '@finpro/ui';
```

### Core Components

**Button**
- `Button` - Primary button component with variants and loading states
- Props: `ButtonProps`

**Form Controls**
- `Input` - Text input field
- `Textarea` - Multi-line text input
- `Checkbox` - Checkbox with label support
- `Switch` - Toggle switch component
- `RadioGroup`, `RadioGroupItem`, `RadioGroupOption` - Radio button groups
- `Slider` - Range slider input
- `Label` - Form field labels
- Props: `CheckboxProps`, `SwitchProps`, `LabelProps`

**Select & Dropdown**
- `Select`, `SelectGroup`, `SelectValue`, `SelectTrigger`, `SelectContent`, `SelectLabel`, `SelectItem`, `SelectSeparator`, `SelectScrollUpButton`, `SelectScrollDownButton` - Select dropdown
- `DropdownMenu`, `DropdownMenuTrigger`, `DropdownMenuContent`, `DropdownMenuItem`, `DropdownMenuCheckboxItem`, `DropdownMenuRadioItem`, `DropdownMenuLabel`, `DropdownMenuSeparator`, `DropdownMenuShortcut`, `DropdownMenuGroup`, `DropdownMenuPortal`, `DropdownMenuSub`, `DropdownMenuSubContent`, `DropdownMenuSubTrigger`, `DropdownMenuRadioGroup` - Context dropdown menus

### Layout Components

**Dialog & Modals**
- `Dialog`, `DialogPortal`, `DialogOverlay`, `DialogClose`, `DialogTrigger`, `DialogContent`, `DialogHeader`, `DialogFooter`, `DialogTitle`, `DialogDescription` - Modal dialogs
- `AlertDialog`, `AlertDialogPortal`, `AlertDialogOverlay`, `AlertDialogTrigger`, `AlertDialogContent`, `AlertDialogHeader`, `AlertDialogFooter`, `AlertDialogTitle`, `AlertDialogDescription`, `AlertDialogAction`, `AlertDialogCancel` - Alert confirmation dialogs

**Overlays**
- `Popover`, `PopoverTrigger`, `PopoverContent`, `PopoverAnchor` - Floating content
- `Tooltip`, `TooltipTrigger`, `TooltipContent`, `TooltipProvider` - Hover tooltips
- `HoverCard`, `HoverCardTrigger`, `HoverCardContent`, `HoverCardArrow`, `HoverCardHeader`, `HoverCardAvatar`, `HoverCardTitle`, `HoverCardDescription`, `HoverCardFooter` - Rich hover cards
- Props: `HoverCardContentProps`

**Containers**
- `Tabs`, `TabsList`, `TabsTrigger`, `TabsContent` - Tab navigation
- `Accordion`, `AccordionItem`, `AccordionTrigger`, `AccordionContent` - Collapsible sections
- `Collapsible`, `CollapsibleTrigger`, `CollapsibleContent` - Simple show/hide content
- `ScrollArea`, `ScrollAreaViewport`, `ScrollAreaScrollbar`, `ScrollAreaThumb`, `ScrollAreaCorner` - Custom scrollbars
- Props: `CollapsibleProps`, `CollapsibleTriggerProps`, `CollapsibleContentProps`, `ScrollAreaProps`, `ScrollAreaScrollbarProps`, `ScrollAreaThumbProps`

### Navigation Components

**Menus**
- `NavigationMenu`, `NavigationMenuList`, `NavigationMenuItem`, `NavigationMenuTrigger`, `NavigationMenuContent`, `NavigationMenuLink`, `NavigationMenuIndicator`, `NavigationMenuViewport`, `NavigationMenuSub`, `NavigationMenuLinkItem`, `NavigationMenuGrid`, `NavigationMenuGridItem` - Complex navigation menus
- `ContextMenu`, `ContextMenuTrigger`, `ContextMenuContent`, `ContextMenuItem`, `ContextMenuCheckboxItem`, `ContextMenuRadioItem`, `ContextMenuLabel`, `ContextMenuSeparator`, `ContextMenuShortcut`, `ContextMenuGroup`, `ContextMenuPortal`, `ContextMenuSub`, `ContextMenuSubContent`, `ContextMenuSubTrigger`, `ContextMenuRadioGroup` - Right-click context menus

**Command Interface**
- `Command`, `CommandInput`, `CommandList`, `CommandEmpty`, `CommandGroup`, `CommandItem`, `CommandShortcut`, `CommandSeparator`, `CommandDialog` - Command palette/search interface
- Props: `CommandProps`

### Data Display

**Media**
- `Avatar`, `AvatarImage`, `AvatarFallback` - User profile pictures
- `AspectRatio` - Maintain aspect ratios for media
- Props: `AspectRatioProps`

**Feedback**
- `Progress`, `CircularProgress` - Progress indicators
- `Toast`, `ToastProvider`, `ToastViewport`, `ToastTitle`, `ToastDescription`, `ToastClose`, `ToastAction` - Toast notifications

**Loading States**
- `Spinner` - Loading spinner
- `LoadingDots` - Animated loading dots
- `Skeleton` - Skeleton loading placeholders
- Props: `SpinnerProps`, `LoadingDotsProps`, `SkeletonProps`

### Utility Components

**Form Helpers**
- `Form`, `FormField`, `FormLabel`, `FormControl`, `FormMessage`, `FormSubmit`, `FormDescription`, `FormValidityState` - Form validation and structure
- Props: `FormFieldProps`, `FormLabelProps`, `FormControlProps`, `FormMessageProps`, `FormSubmitProps`, `FormDescriptionProps`

**UI Elements**
- `Separator` - Visual dividers
- `Toggle` - Toggle button
- `ToggleGroup`, `ToggleMultiGroup`, `ToggleGroupItem` - Toggle button groups
- `VisuallyHidden` - Screen reader only content
- Props: `SeparatorProps`, `ToggleProps`, `ToggleGroupSingleProps`, `ToggleGroupMultipleProps`, `ToggleGroupItemProps`, `VisuallyHiddenProps`

## Usage Examples

### Basic Form
```tsx
import { Button, Input, Label, Form } from '@finpro/ui';

function LoginForm() {
  return (
    <Form>
      <div className="space-y-4">
        <div>
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" />
        </div>
        <div>
          <Label htmlFor="password">Password</Label>
          <Input id="password" type="password" />
        </div>
        <Button type="submit">Sign In</Button>
      </div>
    </Form>
  );
}
```

### Modal Dialog
```tsx
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogDescription, Button } from '@finpro/ui';

function ConfirmDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="destructive">Delete</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Are you sure?</DialogTitle>
          <DialogDescription>
            This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
```

### Data Loading
```tsx
import { Skeleton, Spinner, LoadingDots } from '@finpro/ui';

function LoadingStates() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-4 w-full" />
      <Spinner size="lg" />
      <LoadingDots />
    </div>
  );
}
```

All components are built with Radix UI primitives for accessibility and include comprehensive TypeScript support.