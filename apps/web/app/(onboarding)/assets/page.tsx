'use client'

import { OnboardingStepHeader } from '../components'
import { useOnboardingStore, useOnboardingNavigation } from '../hooks'

// Import the existing dashboard assets page
import DashboardAssetsPage from '@/app/client/dashboard/assets/page'

export default function AssetsPage() {
  const { goNext, goBack, canGoBack } = useOnboardingNavigation()
  const { markStepComplete } = useOnboardingStore()

  const handleSave = () => {
    markStepComplete('assets')
  }

  const handleContinue = () => {
    handleSave()
    goNext()
  }

  const handleBack = () => {
    goBack()
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Assets"
        description="Your investments, savings, and valuable assets"
        onBack={canGoBack ? handleBack : undefined}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      {/* Import the existing dashboard assets functionality */}
      <DashboardAssetsPage />
    </div>
  )
}