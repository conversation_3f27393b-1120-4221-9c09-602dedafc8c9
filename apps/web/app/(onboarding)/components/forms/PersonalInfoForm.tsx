import { Form } from '@finpro/ui'
import { OnboardingStepHeader, OnboardingInput, OnboardingSection } from '../'
import { useOnboardingForm } from '../../hooks'

export function PersonalInfoForm() {
  const {
    formData,
    errors,
    updateField,
    handleSave,
    handleContinue,
    handleBack,
    canGoBack
  } = useOnboardingForm('personal-info')

  const maritalStatusOptions = [
    { value: 'single', label: 'Single' },
    { value: 'married', label: 'Married' },
    { value: 'divorced', label: 'Divorced' },
    { value: 'widowed', label: 'Widow' },
    { value: 'separated', label: 'Separated' }
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleContinue()
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Personal information"
        onBack={canGoBack ? handleBack : undefined}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <Form asChild>
        <form onSubmit={handleSubmit} className="space-y-8">
          <OnboardingSection 
            title="Name"
            description="Please provide your full legal name as it appears on your identification documents."
          >
            <div className="grid grid-cols-3 gap-6">
              <OnboardingInput
                name="firstName"
                label="FIRST"
                value={formData?.firstName || ''}
                onChange={(e) => updateField('firstName', e.target.value)}
                placeholder="First name"
                error={errors.firstName}
                required
              />

              <OnboardingInput
                name="middleName"
                label="MIDDLE"
                value={formData?.middleName || ''}
                onChange={(e) => updateField('middleName', e.target.value)}
                placeholder="Middle name"
              />

              <OnboardingInput
                name="lastName"
                label="LAST"
                value={formData?.lastName || ''}
                onChange={(e) => updateField('lastName', e.target.value)}
                placeholder="Last name"
                error={errors.lastName}
                required
              />
            </div>
          </OnboardingSection>

          <OnboardingSection 
            title="Birthday"
            description="Enter your date of birth to verify your age and identity."
          >
            <div className="max-w-xs">
              <OnboardingInput
                name="dateOfBirth"
                label="BIRTHDAY"
                type="date"
                value={formData?.dateOfBirth || ''}
                onChange={(e) => updateField('dateOfBirth', e.target.value)}
                error={errors.dateOfBirth}
                required
              />
            </div>
          </OnboardingSection>

          <OnboardingSection 
            title="Current marital status"
            description="Select your current marital status for tax and financial planning purposes."
          >
            <div className="flex flex-wrap gap-3">
              {maritalStatusOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => updateField('maritalStatus', option.value)}
                  className={`
                    px-4 py-2 rounded-lg border text-sm font-medium transition-colors
                    ${(formData?.maritalStatus || 'single') === option.value
                      ? 'bg-primary text-primary-foreground border-primary'
                      : 'bg-background text-foreground border-gray-200 hover:bg-accent'
                    }
                  `}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </OnboardingSection>
        </form>
      </Form>
    </div>
  )
}