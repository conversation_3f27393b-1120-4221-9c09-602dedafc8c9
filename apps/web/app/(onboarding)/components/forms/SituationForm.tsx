import { SvgIcon } from '@finpro/ui'
import { OnboardingStepHeader, OnboardingSection } from '../'
import { useOnboardingForm } from '../../hooks'

export function SituationForm() {
  const {
    formData,
    updateField,
    handleSave,
    handleContinue,
    handleBack,
    canGoBack
  } = useOnboardingForm('situation')

  const characteristics = [
    { id: 'married', label: 'Married', icon: 'people' as const },
    { id: 'kids', label: 'Kids', icon: 'people' as const },
    { id: 'military', label: 'Military', icon: 'person' as const },
    { id: 'student', label: 'Student', icon: 'person' as const },
    { id: 'investments', label: 'Investments', icon: 'assets' as const },
    { id: 'business-owner', label: 'Business Owner', icon: 'financial-profile' as const },
    { id: 'retired', label: 'Retired', icon: 'budget' as const },
    { id: 'government', label: 'Government', icon: 'account' as const }
  ]

  const toggleCharacteristic = (characteristicId: string) => {
    const current = formData?.selectedCharacteristics || []
    const updated = current.includes(characteristicId)
      ? current.filter(id => id !== characteristicId)
      : [...current, characteristicId]
    
    updateField('selectedCharacteristics', updated)
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Financial Situation"
        description="Tell us about your current situation"
        onBack={canGoBack ? handleBack : undefined}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <OnboardingSection
        title="What describes your situation?"
        description="Select all that apply to help us provide better recommendations."
      >
        <div className="flex flex-wrap gap-3">
          {characteristics.map((characteristic) => (
            <label
              key={characteristic.id}
              className={`flex flex-col items-center justify-center px-2 py-2 rounded-lg border-2 transition-all duration-200 cursor-pointer w-[120px] h-[122px] ${
                (formData?.selectedCharacteristics || []).includes(characteristic.id)
                  ? 'border-primary bg-primary/10 text-primary shadow-md'
                  : 'border-gray-200 bg-background text-foreground hover:border-primary/30 hover:bg-accent/50'
              }`}
            >
              <input
                type="checkbox"
                checked={(formData?.selectedCharacteristics || []).includes(characteristic.id)}
                onChange={() => toggleCharacteristic(characteristic.id)}
                className="sr-only"
              />
              <SvgIcon 
                shape={characteristic.icon} 
                width={52} 
                height={52} 
                className="mb-1 flex-shrink-0"
                style={{ width: '52px', height: '52px' }}
              />
              <span className="text-xs font-medium text-center leading-tight">{characteristic.label}</span>
            </label>
          ))}
        </div>
      </OnboardingSection>
    </div>
  )
}