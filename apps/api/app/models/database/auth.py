from datetime import datetime
import uuid
import enum

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    DateTime,
    Enum,
    String,
    Text,
    UUID,
    ForeignKey,
    Integer,
    JSON,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class PasswordHashAlgorithm(enum.Enum):
    BCRYPT = "bcrypt"


class VerificationType(enum.Enum):
    REGISTRATION = "REGISTRATION"
    EMAIL_CHANGE = "EMAIL_CHANGE"


class UserPassword(Base):
    __tablename__ = "auth_user_passwords"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    algorithm = Column(Enum(PasswordHashAlgorithm), default=PasswordHashAlgorithm.BCRYPT)
    algorithm_version = Column(Integer, default=1)
    password_history = Column(JSON, default=list)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="passwords")


class EmailVerificationToken(Base):
    __tablename__ = "auth_email_verification_tokens"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token_hash = Column(String(255), nullable=False, index=True)
    verification_type = Column(Enum(VerificationType), default=VerificationType.REGISTRATION)
    issued_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="email_verification_tokens")