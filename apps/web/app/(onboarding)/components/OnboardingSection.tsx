interface OnboardingSectionProps {
  title: string
  description?: string
  children?: React.ReactNode
  className?: string
}

export function OnboardingSection({ 
  title, 
  description, 
  children, 
  className = "" 
}: OnboardingSectionProps) {
  return (
    <div className={className}>
      <h2 className="text-lg font-semibold mb-4">{title}</h2>
      {description && (
        <p className="text-muted-foreground mb-6">
          {description}
        </p>
      )}
      {children}
    </div>
  )
}