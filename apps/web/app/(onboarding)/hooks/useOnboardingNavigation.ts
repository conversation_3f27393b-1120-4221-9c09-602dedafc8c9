'use client'

import { usePathname, useRouter } from 'next/navigation'
import { useMemo, useCallback } from 'react'
import { OnboardingNavigationService } from '../lib/navigation'
import { OnboardingStepId } from '../types'

export interface UseOnboardingNavigationReturn {
  currentStep: OnboardingStepId
  canGoNext: boolean
  canGoBack: boolean
  goNext: () => boolean
  goBack: () => boolean
  goToStep: (stepId: OnboardingStepId) => boolean
  getStepPath: (stepId: OnboardingStepId) => string | null
}

export function useOnboardingNavigation(): UseOnboardingNavigationReturn {
  const pathname = usePathname()
  const router = useRouter()

  // Create navigation service instance
  const navigationService = useMemo(() => new OnboardingNavigationService(router), [router])

  // Derive current step from URL pathname - single source of truth
  const currentStep = useMemo(() => navigationService.getCurrentStep(pathname), [navigationService, pathname])

  // Check navigation capabilities
  const canGoNext = useMemo(() => navigationService.canNavigate(currentStep, 'next'), [navigationService, currentStep])
  const canGoBack = useMemo(() => navigationService.canNavigate(currentStep, 'prev'), [navigationService, currentStep])

  // Navigation methods
  const goNext = useCallback(() => navigationService.navigate(currentStep, 'next'), [navigationService, currentStep])
  const goBack = useCallback(() => navigationService.navigate(currentStep, 'prev'), [navigationService, currentStep])
  const goToStep = useCallback((stepId: OnboardingStepId) => navigationService.goToStep(stepId), [navigationService])
  const getStepPath = useCallback((stepId: OnboardingStepId) => navigationService.getStepPath(stepId), [navigationService])

  return {
    currentStep,
    canGoNext,
    canGoBack,
    goNext,
    goBack,
    goToStep,
    getStepPath
  }
}