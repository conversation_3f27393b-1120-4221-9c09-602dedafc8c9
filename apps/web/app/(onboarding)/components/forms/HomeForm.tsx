import { Form, FormField, FormLabel, FormControl, FormMessage, Button, Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@finpro/ui'
import { OnboardingStepHeader, OnboardingInput, OnboardingSection } from '../'
import { useOnboardingForm } from '../../hooks'
import { useMemo } from 'react'

export function HomeForm() {
  const {
    formData,
    errors,
    updateField,
    handleSave,
    handleContinue,
    handleBack,
    canGoBack
  } = useOnboardingForm('home')


  const annualRentCost = useMemo(() => {
    if (!formData?.rentPayment || !formData?.rentInterval) return 0
    
    switch (formData.rentInterval) {
      case 'weekly':
        return formData.rentPayment * 52
      case 'monthly':
        return formData.rentPayment * 12
      case 'yearly':
        return formData.rentPayment
      default:
        return 0
    }
  }, [formData?.rentPayment, formData?.rentInterval])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleContinue()
  }

  return (
    <div>
      <OnboardingStepHeader
        title="Primary Residence"
        description="Tell us about your housing situation"
        onBack={canGoBack ? handleBack : undefined}
        onSave={handleSave}
        onContinue={handleContinue}
      />

      <Form asChild>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-12 gap-8 min-h-[600px]">
            {/* Left Side - Housing Status Selection */}
            <div className="col-span-4">
              <FormField name="housingStatus">
                <FormLabel required className="text-lg font-semibold mb-4 block">
                  What is your housing situation?
                </FormLabel>
                <div className="space-y-4">
                  <Button
                    type="button"
                    variant={formData?.housingStatus === 'own' ? 'default' : 'outlined'}
                    className="w-full h-auto p-6 flex flex-col items-center justify-center gap-4 min-h-[120px]"
                    onClick={() => updateField('housingStatus', 'own')}
                  >
                    <svg 
                      width="52" 
                      height="52" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="1.5" 
                      className="lucide lucide-home"
                    >
                      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                      <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <div className="font-semibold text-base text-center leading-tight">I own my home</div>
                  </Button>
                  
                  <Button
                    type="button"
                    variant={formData?.housingStatus === 'rent' ? 'default' : 'outlined'}
                    className="w-full h-auto p-6 flex flex-col items-center justify-center gap-4 min-h-[120px]"
                    onClick={() => updateField('housingStatus', 'rent')}
                  >
                    <svg 
                      width="52" 
                      height="52" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="1.5" 
                      className="lucide lucide-building"
                    >
                      <rect width="16" height="20" x="4" y="2" rx="2" ry="2"/>
                      <path d="M9 22v-4h6v4"/>
                      <path d="M8 6h.01"/>
                      <path d="M16 6h.01"/>
                      <path d="M12 6h.01"/>
                      <path d="M12 10h.01"/>
                      <path d="M12 14h.01"/>
                      <path d="M16 10h.01"/>
                      <path d="M16 14h.01"/>
                      <path d="M8 10h.01"/>
                      <path d="M8 14h.01"/>
                    </svg>
                    <div className="font-semibold text-base text-center leading-tight">I rent a home</div>
                  </Button>
                </div>
                {errors.housingStatus && (
                  <FormMessage variant="error" className="mt-2">{errors.housingStatus}</FormMessage>
                )}
              </FormField>
            </div>

            {/* Right Side - Conditional Forms */}
            <div className="col-span-8 pl-8">
              {formData?.housingStatus && (
                <div className="space-y-6">
                  {/* Owner Details */}
                  {formData?.housingStatus === 'own' && (
                    <OnboardingSection title="🏠 Homeowner Details">
                      <div className="space-y-6">
                    
                    <OnboardingInput
                      name="address"
                      label="Property Address"
                      type="text"
                      value={formData?.address || ''}
                      onChange={(e) => updateField('address', e.target.value)}
                      placeholder="123 Main Street, City, State 12345"
                      error={errors.address}
                      required
                    />

                    <OnboardingInput
                      name="estimatedValue"
                      label="Estimated Value"
                      type="number"
                      min="0"
                      step="1000"
                      value={formData?.estimatedValue || ''}
                      onChange={(e) => updateField('estimatedValue', parseFloat(e.target.value) || 0)}
                      placeholder="500000"
                      error={errors.estimatedValue}
                      required
                    />

                    <OnboardingInput
                      name="dateAcquired"
                      label="Date Acquired"
                      type="date"
                      value={formData?.dateAcquired || ''}
                      onChange={(e) => updateField('dateAcquired', e.target.value)}
                      error={errors.dateAcquired}
                      required
                    />

                      </div>
                    </OnboardingSection>
                  )}

                {/* Renter Details */}
                {formData?.housingStatus === 'rent' && (
                  <OnboardingSection title="🏢 Renter Details">
                    <div className="space-y-6">
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <OnboardingInput
                        name="rentPayment"
                        label="Rent Payment Amount"
                        type="number"
                        min="0"
                        step="50"
                        value={formData?.rentPayment || ''}
                        onChange={(e) => updateField('rentPayment', parseFloat(e.target.value) || 0)}
                        placeholder="2500"
                        error={errors.rentPayment}
                        required
                      />

                      <FormField name="rentInterval">
                        <FormLabel htmlFor="rentInterval" required>
                          Payment Interval
                        </FormLabel>
                        <FormControl asChild>
                          <Select
                            value={formData?.rentInterval || ''}
                            onValueChange={(value) => updateField('rentInterval', value)}
                          >
                            <SelectTrigger className={errors.rentInterval ? 'border-error' : ''}>
                              <SelectValue placeholder="Select interval" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="monthly">Monthly</SelectItem>
                              <SelectItem value="yearly">Yearly</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        {errors.rentInterval && (
                          <FormMessage variant="error">{errors.rentInterval}</FormMessage>
                        )}
                      </FormField>
                    </div>

                    {/* Annual Rent Cost */}
                    {(formData?.rentPayment || 0) > 0 && formData?.rentInterval && (
                      <div className="bg-accent/10 rounded-lg p-6 border border-accent/20">
                        <h4 className="text-base font-semibold text-foreground mb-3">Annual Housing Cost</h4>
                        <div className="text-2xl font-bold text-primary mb-2">
                          ${annualRentCost.toLocaleString()}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          ${(formData?.rentPayment || 0).toLocaleString()} {formData?.rentInterval} × {formData?.rentInterval === 'weekly' ? '52' : formData?.rentInterval === 'monthly' ? '12' : '1'} {formData?.rentInterval === 'yearly' ? '' : formData?.rentInterval === 'weekly' ? 'weeks' : 'months'}
                        </p>
                      </div>
                    )}
                    </div>
                  </OnboardingSection>
                )}
                </div>
              )}

              {/* Empty state when no housing status selected */}
              {!formData?.housingStatus && (
                <div className="flex items-center justify-center h-64 text-center">
                  <div className="text-muted-foreground">
                    <svg 
                      width="64" 
                      height="64" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="1" 
                      className="mx-auto mb-4 opacity-50"
                    >
                      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                      <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <p className="text-lg">Select your housing situation to continue</p>
                    <p className="text-sm mt-2">Choose from the options on the left</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}