'use client'

import { create } from 'zustand'
import { persist, PersistStorage } from 'zustand/middleware'
import { OnboardingStepId, OnboardingFormData } from '../app/(onboarding)/types'
import { ONBOARDING_STEPS } from '../app/(onboarding)/constants'
import { validationSchemas } from '../app/(onboarding)/lib/formValidation'
import { ZodError } from 'zod'

/**
 * Debounce utility function for store-level auto-save functionality
 * Creates a debounced version of a function that delays execution until after
 * the specified delay has passed since the last invocation.
 */
function debounce<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): T & { cancel: () => void } {
  let timeoutId: NodeJS.Timeout | null = null

  const debouncedFunction = ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      timeoutId = null
      func(...args)
    }, delay)
  }) as T & { cancel: () => void }

  // Add cancel method to clear pending executions
  debouncedFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  return debouncedFunction
}

/**
 * Maps step IDs to their corresponding data section keys
 * This centralizes the mapping logic that was previously duplicated
 * across components and store methods.
 */
function getSectionKey(stepId: OnboardingStepId): keyof OnboardingFormData {
  switch (stepId) {
    case 'personal-info':
      return 'personalInfo'
    case 'home':
      return 'homeInfo'
    default:
      return stepId as keyof OnboardingFormData
  }
}

interface ValidationErrors {
  [stepId: string]: {
    [field: string]: string
  }
}

interface OnboardingStore {
  // Core state
  data: Partial<OnboardingFormData>
  completedSteps: Set<OnboardingStepId>
  errors: ValidationErrors

  // Actions
  updateData: (section: keyof OnboardingFormData, data: any) => void
  updateField: (stepId: OnboardingStepId, field: string, value: any) => void
  validateStep: (stepId: OnboardingStepId) => boolean
  clearErrors: (stepId: OnboardingStepId, field?: string) => void
  markStepComplete: (step: OnboardingStepId) => void
  resetOnboarding: () => void

  // Internal methods (not exposed to components)
  _debouncedAutoSave: (sectionKey: keyof OnboardingFormData) => void
  _cancelAutoSave: () => void
}

const storage: PersistStorage<OnboardingStore> = {
  getItem: (name) => {
    const str = typeof window !== 'undefined' ? localStorage.getItem(name) : null
    if (!str) return null
    const { state, version } = JSON.parse(str)
    return {
      state: {
        ...state,
        completedSteps: new Set(state.completedSteps),
        errors: state.errors || {},
      },
      version,
    }
  },
  setItem: (name, value) => {
    if (typeof window !== 'undefined') {
      const { state, version } = value
      const serializedState = {
        state: {
          ...state,
          completedSteps: Array.from(state.completedSteps),
          errors: state.errors,
        },
        version,
      }
      localStorage.setItem(name, JSON.stringify(serializedState))
    }
  },
  removeItem: (name) => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(name)
    }
  },
}

export const useOnboardingStore = create<OnboardingStore>()(
  persist(
    (set, get) => {
      /**
       * Store-level debounced auto-save function
       * This handles automatic persistence of form data after user stops typing.
       * By implementing this at the store level, we eliminate React closure issues
       * and ensure consistent behavior across all components.
       */
      const debouncedAutoSave = debounce((sectionKey: keyof OnboardingFormData) => {
        const currentState = get()
        const sectionData = currentState.data[sectionKey]

        if (sectionData) {
          // Here you would typically persist to backend API
          // For now, we'll just log the auto-save action
          console.log(`[Auto-Save] Persisting ${sectionKey}:`, sectionData)

          // Example: API call would go here
          // await api.saveOnboardingSection(sectionKey, sectionData)

          // The data is already in the store from updateField,
          // so no additional store update is needed here
        }
      }, 1000) // 1 second debounce delay

      return {
        // Initial state
        data: {},
        completedSteps: new Set<OnboardingStepId>(),
        errors: {},

        // Actions
        updateData: (section, data) =>
          set(state => ({
            data: { ...state.data, [section]: data }
          })),

        /**
         * Updates a single field in the form data with immediate store update
         * and triggers debounced auto-save for persistence.
         * This eliminates the need for component-level debouncing.
         */
        updateField: (stepId, field, value) => {
          // Get the section key using centralized mapping
          const sectionKey = getSectionKey(stepId)

          // Immediate store update for optimistic UI
          set(state => {
            const currentSectionData = state.data[sectionKey] || {}
            const updatedSectionData = { ...currentSectionData, [field]: value }

            return {
              data: {
                ...state.data,
                [sectionKey]: updatedSectionData
              },
              // Clear field error when updating
              errors: {
                ...state.errors,
                [stepId]: {
                  ...(state.errors[stepId] || {}),
                  [field]: ''
                }
              }
            }
          })

          // Trigger debounced auto-save for persistence
          // This will automatically cancel previous pending saves for the same section
          debouncedAutoSave(sectionKey)
        },

        validateStep: (stepId) => {
          const schema = validationSchemas[stepId as keyof typeof validationSchemas]
          if (!schema) return true

          const sectionKey = getSectionKey(stepId)
          const stepData = get().data[sectionKey] || {}

          try {
            schema.parse(stepData)
            // Clear errors on successful validation
            set(state => ({
              errors: {
                ...state.errors,
                [stepId]: {}
              }
            }))
            return true
          } catch (error) {
            if (error instanceof ZodError) {
              const fieldErrors: { [field: string]: string } = {}
              error.issues.forEach(err => {
                if (err.path.length > 0) {
                  const field = err.path[0] as string
                  fieldErrors[field] = err.message
                }
              })

              set(state => ({
                errors: {
                  ...state.errors,
                  [stepId]: fieldErrors
                }
              }))
            }
            return false
          }
        },

        clearErrors: (stepId, field) =>
          set(state => {
            if (field) {
              return {
                errors: {
                  ...state.errors,
                  [stepId]: {
                    ...(state.errors[stepId] || {}),
                    [field]: ''
                  }
                }
              }
            } else {
              return {
                errors: {
                  ...state.errors,
                  [stepId]: {}
                }
              }
            }
          }),

        markStepComplete: (step) =>
          set(state => ({
            completedSteps: new Set([...Array.from(state.completedSteps), step])
          })),

        resetOnboarding: () => {
          // Cancel any pending auto-saves before resetting
          debouncedAutoSave.cancel()

          set({
            data: {},
            completedSteps: new Set<OnboardingStepId>(),
            errors: {}
          })
        },

        // Internal methods for store-level debouncing
        // These are not intended for direct component use
        _debouncedAutoSave: debouncedAutoSave,
        _cancelAutoSave: () => debouncedAutoSave.cancel()
      }
    },
    {
      name: 'onboarding-storage',
      storage,
    }
  )
)

// Computed selectors
export const useOnboardingProgress = () => {
  const completedSteps = useOnboardingStore(state => state.completedSteps)
  return Math.round((completedSteps.size / ONBOARDING_STEPS.length) * 100)
}


