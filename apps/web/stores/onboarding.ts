'use client'

import { create } from 'zustand'
import { persist, PersistStorage } from 'zustand/middleware'
import { OnboardingStepId, OnboardingFormData } from '../app/(onboarding)/types'
import { ONBOARDING_STEPS } from '../app/(onboarding)/constants'
import { validationSchemas } from '../app/(onboarding)/lib/formValidation'
import { ZodError } from 'zod'

interface ValidationErrors {
  [stepId: string]: {
    [field: string]: string
  }
}

interface OnboardingStore {
  // Core state
  data: Partial<OnboardingFormData>
  completedSteps: Set<OnboardingStepId>
  errors: ValidationErrors

  // Actions
  updateData: (section: keyof OnboardingFormData, data: any) => void
  updateField: (stepId: OnboardingStepId, field: string, value: any) => void
  validateStep: (stepId: OnboardingStepId) => boolean
  clearErrors: (stepId: OnboardingStepId, field?: string) => void
  markStepComplete: (step: OnboardingStepId) => void
  resetOnboarding: () => void
}

const storage: PersistStorage<OnboardingStore> = {
  getItem: (name) => {
    const str = typeof window !== 'undefined' ? localStorage.getItem(name) : null
    if (!str) return null
    const { state, version } = JSON.parse(str)
    return {
      state: {
        ...state,
        completedSteps: new Set(state.completedSteps),
        errors: state.errors || {},
      },
      version,
    }
  },
  setItem: (name, value) => {
    if (typeof window !== 'undefined') {
      const { state, version } = value
      const serializedState = {
        state: {
          ...state,
          completedSteps: Array.from(state.completedSteps),
          errors: state.errors,
        },
        version,
      }
      localStorage.setItem(name, JSON.stringify(serializedState))
    }
  },
  removeItem: (name) => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(name)
    }
  },
}

export const useOnboardingStore = create<OnboardingStore>()(
  persist(
    (set, get) => ({
      // Initial state
      data: {},
      completedSteps: new Set<OnboardingStepId>(),
      errors: {},

      // Actions
      updateData: (section, data) =>
        set(state => ({
          data: { ...state.data, [section]: data }
        })),

      updateField: (stepId, field, value) =>
        set(state => {
          const sectionKey = stepId === 'personal-info' ? 'personalInfo' :
                           stepId === 'home' ? 'homeInfo' :
                           stepId as keyof OnboardingFormData
          
          const currentSectionData = state.data[sectionKey] || {}
          const updatedSectionData = { ...currentSectionData, [field]: value }
          
          return {
            data: {
              ...state.data,
              [sectionKey]: updatedSectionData
            },
            // Clear field error when updating
            errors: {
              ...state.errors,
              [stepId]: {
                ...(state.errors[stepId] || {}),
                [field]: ''
              }
            }
          }
        }),

      validateStep: (stepId) => {
        const schema = validationSchemas[stepId as keyof typeof validationSchemas]
        if (!schema) return true

        const sectionKey = stepId === 'personal-info' ? 'personalInfo' :
                         stepId === 'home' ? 'homeInfo' :
                         stepId as keyof OnboardingFormData
        
        const stepData = get().data[sectionKey] || {}
        
        try {
          schema.parse(stepData)
          // Clear errors on successful validation
          set(state => ({
            errors: {
              ...state.errors,
              [stepId]: {}
            }
          }))
          return true
        } catch (error) {
          if (error instanceof ZodError) {
            const fieldErrors: { [field: string]: string } = {}
            error.issues.forEach(err => {
              if (err.path.length > 0) {
                const field = err.path[0] as string
                fieldErrors[field] = err.message
              }
            })
            
            set(state => ({
              errors: {
                ...state.errors,
                [stepId]: fieldErrors
              }
            }))
          }
          return false
        }
      },

      clearErrors: (stepId, field) =>
        set(state => {
          if (field) {
            return {
              errors: {
                ...state.errors,
                [stepId]: {
                  ...(state.errors[stepId] || {}),
                  [field]: ''
                }
              }
            }
          } else {
            return {
              errors: {
                ...state.errors,
                [stepId]: {}
              }
            }
          }
        }),

      markStepComplete: (step) =>
        set(state => ({
          completedSteps: new Set([...Array.from(state.completedSteps), step])
        })),

      resetOnboarding: () => set({
        data: {},
        completedSteps: new Set<OnboardingStepId>(),
        errors: {}
      })
    }),
    {
      name: 'onboarding-storage',
      storage,
    }
  )
)

// Computed selectors
export const useOnboardingProgress = () => {
  const completedSteps = useOnboardingStore(state => state.completedSteps)
  return Math.round((completedSteps.size / ONBOARDING_STEPS.length) * 100)
}


