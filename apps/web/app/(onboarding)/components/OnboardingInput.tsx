import { Input, FormField, FormLabel, FormControl, FormMessage } from '@finpro/ui'
import { InputHTMLAttributes } from 'react'

interface OnboardingInputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'size'> {
  name: string
  label: string
  error?: string
  required?: boolean
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
}

export function OnboardingInput({ 
  name, 
  label, 
  error, 
  required, 
  onChange,
  className = "mt-2",
  ...props 
}: OnboardingInputProps) {
  return (
    <FormField name={name}>
      <FormLabel 
        htmlFor={name} 
        className="text-sm font-medium text-muted-foreground"
        required={required}
      >
        {label}
      </FormLabel>
      <FormControl asChild>
        <Input
          id={name}
          onChange={onChange}
          variant={error ? 'error' : 'default'}
          className={className}
          {...props}
        />
      </FormControl>
      {error && (
        <FormMessage variant="error">{error}</FormMessage>
      )}
    </FormField>
  )
}