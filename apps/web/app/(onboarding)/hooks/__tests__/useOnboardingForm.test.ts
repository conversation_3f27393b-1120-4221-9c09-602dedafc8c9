/**
 * Test file for the enhanced useOnboardingForm hook with store-level debouncing
 * 
 * This test verifies that the new implementation:
 * 1. Eliminates stale closure issues
 * 2. <PERSON><PERSON><PERSON> handles debounced auto-save at the store level
 * 3. Maintains backward compatibility with the existing interface
 * 4. <PERSON><PERSON><PERSON> cleans up on component unmount
 */

import { renderHook, act } from '@testing-library/react'
import { useOnboardingForm } from '../useOnboardingForm'
import { useOnboardingStore } from '../../../../stores/onboarding'

// Mock the navigation hook
jest.mock('../useOnboardingNavigation', () => ({
  useOnboardingNavigation: () => ({
    goNext: jest.fn(),
    goBack: jest.fn(),
    canGoNext: true,
    canGoBack: true
  })
}))

describe('useOnboardingForm with Store-Level Debouncing', () => {
  beforeEach(() => {
    // Reset store state before each test
    useOnboardingStore.getState().resetOnboarding()
  })

  afterEach(() => {
    // Cancel any pending auto-saves after each test
    useOnboardingStore.getState()._cancelAutoSave()
  })

  it('should update field immediately in store', () => {
    const { result } = renderHook(() => useOnboardingForm('personal-info'))
    
    act(() => {
      result.current.updateField('firstName', 'John')
    })

    // Verify immediate store update
    const storeData = useOnboardingStore.getState().data.personalInfo
    expect(storeData?.firstName).toBe('John')
  })

  it('should handle rapid field updates without race conditions', () => {
    const { result } = renderHook(() => useOnboardingForm('personal-info'))
    
    // Simulate rapid typing
    act(() => {
      result.current.updateField('firstName', 'J')
      result.current.updateField('firstName', 'Jo')
      result.current.updateField('firstName', 'Joh')
      result.current.updateField('firstName', 'John')
    })

    // Verify final state is correct
    const storeData = useOnboardingStore.getState().data.personalInfo
    expect(storeData?.firstName).toBe('John')
  })

  it('should use current store data in handleSave', () => {
    const { result } = renderHook(() => useOnboardingForm('personal-info'))
    
    // Update field through store directly (simulating external update)
    act(() => {
      useOnboardingStore.getState().updateField('personal-info', 'firstName', 'Jane')
    })

    // handleSave should use current store data, not stale closure data
    act(() => {
      result.current.handleSave()
    })

    // Verify the step is marked complete
    const completedSteps = useOnboardingStore.getState().completedSteps
    expect(completedSteps.has('personal-info')).toBe(true)
  })

  it('should cancel auto-save on unmount', () => {
    const { unmount } = renderHook(() => useOnboardingForm('personal-info'))
    
    // Spy on the cancel method
    const cancelSpy = jest.spyOn(useOnboardingStore.getState(), '_cancelAutoSave')
    
    unmount()
    
    expect(cancelSpy).toHaveBeenCalled()
  })

  it('should maintain backward compatibility with existing interface', () => {
    const { result } = renderHook(() => useOnboardingForm('personal-info'))
    
    // Verify all expected methods and properties exist
    expect(result.current).toHaveProperty('formData')
    expect(result.current).toHaveProperty('errors')
    expect(result.current).toHaveProperty('updateField')
    expect(result.current).toHaveProperty('validate')
    expect(result.current).toHaveProperty('isValid')
    expect(result.current).toHaveProperty('handleSave')
    expect(result.current).toHaveProperty('handleContinue')
    expect(result.current).toHaveProperty('handleBack')
    expect(result.current).toHaveProperty('canGoBack')
    expect(result.current).toHaveProperty('canGoNext')
    
    // Verify method signatures
    expect(typeof result.current.updateField).toBe('function')
    expect(typeof result.current.handleSave).toBe('function')
  })
})
