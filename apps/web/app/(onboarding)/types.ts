
export interface UserData {
  assets: number
  liabilities: number
  netWorth: number
}

export interface OnboardingFormData {
  personalInfo?: PersonalInfoData
  situation?: SituationData
  homeInfo?: HomeInfoData
  assets?: AssetsData
  liabilities?: LiabilitiesData
  insurance?: InsuranceData
  income?: IncomeData
  expenses?: ExpensesData
}

export type OnboardingStepId = 'personal-info' | 'situation' | 'home' | 'assets' | 'liabilities' | 'insurance' | 'income' | 'expenses' | 'review'

export interface OnboardingStep {
  id: OnboardingStepId
  title: string
  description: string
  path: string
}

export interface PersonalInfoData {
  firstName: string
  middleName: string
  lastName: string
  dateOfBirth: string
  maritalStatus: 'single' | 'married' | 'divorced' | 'widowed' | 'separated'
}

export interface FinancialGoalsData {
  retirementAge: number
  targetSavings: number
  riskTolerance: 'conservative' | 'moderate' | 'aggressive'
  investmentTimeline?: number
  primaryGoals?: string[]
}

export interface HomeInfoData {
  housingStatus: 'own' | 'rent' | null
  // Owner fields
  address?: string
  estimatedValue?: number
  dateAcquired?: string
  // Renter fields
  rentPayment?: number
  rentInterval?: 'weekly' | 'monthly' | 'yearly'
}

export interface IncomeData {
  salary: number
  bonuses: number
  businessIncome?: number
  investmentIncome?: number
  rentalIncome?: number
  otherIncome: number
  otherIncomeDescription?: string
}

export interface ExpensesData {
  housing: number
  transportation: number
  food: number
  utilities: number
  healthcare?: number
  insurance?: number
  entertainment: number
  shopping?: number
  childcare?: number
  other: number
  otherDescription?: string
}

export interface SituationData {
  selectedCharacteristics?: string[]
  retirementAge?: number
  targetSavings?: number
  riskTolerance?: 'conservative' | 'moderate' | 'aggressive'
  investmentTimeline?: number
  primaryGoals?: string[]
  currentNetWorth?: number
}

export interface AssetsData {
  checkingAccounts?: number
  savingsAccounts?: number
  investmentAccounts?: number
  retirementAccounts?: number
  otherAssets?: number
  otherAssetsDescription?: string
}

export interface LiabilitiesData {
  creditCards?: number
  studentLoans?: number
  autoLoans?: number
  personalLoans?: number
  otherDebt?: number
  otherDebtDescription?: string
}

export interface InsuranceData {
  healthInsurance?: boolean
  lifeInsurance?: number
  disabilityInsurance?: boolean
  autoInsurance?: number
  umbrellaPolicy?: boolean
  otherInsurance?: string
}

export interface LinkedAccount {
  id: string
  institutionName: string
  accountType: 'checking' | 'savings' | 'credit' | 'investment' | 'loan'
  accountName: string
  balance?: number
  isActive: boolean
  lastSync?: Date
}

